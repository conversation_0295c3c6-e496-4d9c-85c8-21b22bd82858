import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Shared Components
import { AddCashModalService } from '../../../shared/components/add-cash-modal/add-cash-modal.service';
import type { AddCashResult, AddCashDialogData } from '../../../shared/components/add-cash-modal/add-cash-modal.component';
import type { CashType } from '../../../shared/models/cashtype.model';

@Component({
  selector: 'app-inventory-overview',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTabsModule,
    MatProgressBarModule,
    MatSnackBarModule
  ],
  templateUrl: './inventory-overview.component.html',
  styleUrls: ['./inventory-overview.component.scss']
})
export class InventoryOverviewComponent implements OnInit {
  // Mock data for demonstration
  inventorySummary = {
    totalValue: 1250000,
    totalNotes: 12500,
    lowStockAlerts: [
      { inventoryId: '1', severity: 'high' as const, message: 'R200 notes running low' }
    ]
  };

  inventoryBreakdown: { [key: string]: any[] } = {
    'MANDELA': [
      {
        id: '1',
        noteSeries: 'MANDELA',
        denomination: 200,
        quantity: 150,
        value: 30000
      },
      {
        id: '2',
        noteSeries: 'MANDELA',
        denomination: 100,
        quantity: 300,
        value: 30000
      }
    ],
    'BIG_5': [],
    'COMMEMORATIVE': [],
    'V6': []
  };

  // Constants for templates
  NOTE_SERIES_LABELS: { [key: string]: string } = {
    'MANDELA': 'Mandela Series',
    'BIG_5': 'Big 5 Series',
    'COMMEMORATIVE': 'Commemorative Series',
    'V6': 'V6 Series'
  };

  DENOMINATION_LABELS: { [key: number]: string } = {
    10: 'R10',
    20: 'R20',
    50: 'R50',
    100: 'R100',
    200: 'R200'
  };

  constructor(
    private addCashModalService: AddCashModalService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Component initialization
  }

  getSeriesArray(): string[] {
    return Object.keys(this.inventoryBreakdown);
  }

  getSeriesTotal(series: string): { quantity: number; value: number } {
    const items = this.inventoryBreakdown[series];
    return {
      quantity: items.reduce((sum: number, item: any) => sum + item.quantity, 0),
      value: items.reduce((sum: number, item: any) => sum + item.value, 0)
    };
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-ZA').format(num);
  }

  getDenominationLabel(denomination: number): string {
    return this.DENOMINATION_LABELS[denomination] || `R${denomination}`;
  }

  formatQuantityDisplay(quantity: number): string {
    const batches = Math.floor(quantity / 100);
    const singles = quantity % 100;

    if (batches === 0) {
      return `${singles} single${singles !== 1 ? 's' : ''}`;
    } else if (singles === 0) {
      return `${batches} batch${batches !== 1 ? 'es' : ''}`;
    } else {
      return `${batches} batch${batches !== 1 ? 'es' : ''} + ${singles} single${singles !== 1 ? 's' : ''}`;
    }
  }

  formatSeriesQuantityDisplay(series: string): string {
    const total = this.getSeriesTotal(series);
    return this.formatQuantityDisplay(total.quantity);
  }

  getStockStatus(item: any): { status: string; class: string } {
    if (item.quantity < 100) {
      return { status: 'Low Stock', class: 'low-stock' };
    } else if (item.quantity > 500) {
      return { status: 'High Stock', class: 'high-stock' };
    }
    return { status: 'Normal', class: 'normal-stock' };
  }

  getStockStatusIcon(item: any): string {
    const status = this.getStockStatus(item);
    if (status.class === 'low-stock') return 'warning';
    if (status.class === 'high-stock') return 'trending_up';
    return 'check_circle';
  }

  getStockPercentage(item: any): number {
    const maxStock = 1000; // Assume max stock is 1000
    return Math.min((item.quantity / maxStock) * 100, 100);
  }

  getStockProgressColor(item: any): string {
    const percentage = this.getStockPercentage(item);
    if (percentage < 20) return 'warn';
    if (percentage > 80) return 'accent';
    return 'primary';
  }

  getSeriesStyleClass(series: string): string {
    return `series-${series.toLowerCase()}`;
  }

  getDenominationImage(denomination: number, series?: string): string {
    return `assets/images/Money/R${denomination}.jpg`;
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  onAddCash(series?: string, denomination?: number): void {
    // Create cash type based on series
    const cashType: CashType = {
      id: series || 'UNKNOWN_SERIES',
      name: this.NOTE_SERIES_LABELS[series || ''] || 'Unknown Series',
      currency: 'ZAR',
      description: `${this.NOTE_SERIES_LABELS[series || '']} - South African Rand`
    };

    // Configure modal data
    const modalData: AddCashDialogData = {
      title: `Add Cash - ${cashType.name}${denomination ? ` (R${denomination})` : ''}`,
      cashTypes: [cashType]
    };

    // Open the modal
    this.addCashModalService.openAddCashModal(modalData)
      .subscribe(result => {
        if (result) {
          this.handleAddCashResult(result, series, denomination);
        }
      });
  }

  private handleAddCashResult(result: AddCashResult, series?: string, denomination?: number): void {
    const action = result.transactionType === 'Addition' ? 'Added' : 'Removed';
    const totalValue = result.denomination * result.quantity;

    // Show success message
    this.snackBar.open(
      `${action} ${result.quantity} x R${result.denomination} notes (Total: R${totalValue.toLocaleString()})`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Cash transaction completed:', {
      result,
      series,
      originalDenomination: denomination,
      timestamp: new Date().toISOString()
    });

    // Here you would typically call a service to update the backend
    // this.inventoryService.updateCashInventory(result);

    // For now, we'll just refresh the component data
    this.refreshInventoryData();
  }

  private refreshInventoryData(): void {
    // In a real application, you would reload data from the backend
    // For now, we'll just log that a refresh would happen
    console.log('Inventory data would be refreshed from backend');
  }

  userService = {
    hasManagerPrivileges: () => true
  };
}
