# Add Cash Modal Integration Guide

This guide shows how to integrate the Add Cash Modal into different parts of the FFA CMS application.

## Quick Start

### 1. Import the Service
```typescript
import { AddCashModalService } from '@shared/components';
```

### 2. Inject in Constructor
```typescript
constructor(private addCashModalService: AddCashModalService) {}
```

### 3. Open Modal
```typescript
openAddCashModal() {
  this.addCashModalService.openAddCashModal()
    .subscribe(result => {
      if (result) {
        this.handleCashTransaction(result);
      }
    });
}
```

## Integration Examples

### Cash Issuer - Inventory Management

```typescript
// src/app/features/cash-issuer/inventory-management/inventory-management.component.ts
import { Component } from '@angular/core';
import { AddCashModalService, AddCashResult } from '@shared/components';
import { CashType } from '@shared/models';

@Component({
  selector: 'app-inventory-management',
  template: `
    <button mat-raised-button color="primary" (click)="addCashToInventory()">
      <mat-icon>add</mat-icon>
      Add Cash to Inventory
    </button>
  `
})
export class InventoryManagementComponent {
  constructor(private addCashModalService: AddCashModalService) {}

  addCashToInventory() {
    const availableCashTypes: CashType[] = [
      { id: 'ZAR_NOTES', name: 'ZAR Notes', currency: 'ZAR' },
      { id: 'USD_NOTES', name: 'USD Notes', currency: 'USD' }
    ];

    this.addCashModalService.openAddCashModalWithTypes(
      availableCashTypes, 
      'Add Cash to Inventory'
    ).subscribe(result => {
      if (result) {
        this.updateInventory(result);
      }
    });
  }

  private updateInventory(transaction: AddCashResult) {
    // Call your inventory service to update the cash amounts
    console.log('Updating inventory:', transaction);
    // this.inventoryService.updateCash(transaction);
  }
}
```

### QA Manager - Inventory Overview

```typescript
// src/app/features/qa-manager/inventory-overview/inventory-overview.component.ts
import { Component } from '@angular/core';
import { AddCashModalService, AddCashResult } from '@shared/components';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-inventory-overview',
  template: `
    <div class="action-buttons">
      <button mat-fab color="primary" (click)="quickAddCash()" 
              matTooltip="Quick Add Cash">
        <mat-icon>add</mat-icon>
      </button>
    </div>
  `
})
export class InventoryOverviewComponent {
  constructor(
    private addCashModalService: AddCashModalService,
    private snackBar: MatSnackBar
  ) {}

  quickAddCash() {
    this.addCashModalService.openAddCashModal({
      title: 'Quick Add Cash'
    }).subscribe(result => {
      if (result) {
        this.showSuccessMessage(result);
        this.refreshInventoryData();
      }
    });
  }

  private showSuccessMessage(result: AddCashResult) {
    const action = result.transactionType === 'Addition' ? 'Added' : 'Removed';
    const total = result.denomination * result.quantity;
    
    this.snackBar.open(
      `${action} R${total} (${result.quantity} x R${result.denomination})`,
      'Close',
      { duration: 3000 }
    );
  }

  private refreshInventoryData() {
    // Refresh your inventory data
  }
}
```

### Cash Requester - Request Cash

```typescript
// src/app/features/cash-requester/request-cash/request-cash.component.ts
import { Component } from '@angular/core';
import { AddCashModalService, AddCashResult } from '@shared/components';

@Component({
  selector: 'app-request-cash',
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Request Cash</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <button mat-raised-button color="accent" (click)="requestSpecificCash()">
          Request ZAR Notes
        </button>
      </mat-card-content>
    </mat-card>
  `
})
export class RequestCashComponent {
  constructor(private addCashModalService: AddCashModalService) {}

  requestSpecificCash() {
    const zarNotes: CashType = { 
      id: 'ZAR_NOTES', 
      name: 'South African Rand Notes',
      currency: 'ZAR'
    };

    this.addCashModalService.openAddCashModalForType(
      zarNotes, 
      'Request ZAR Notes'
    ).subscribe(result => {
      if (result) {
        this.submitCashRequest(result);
      }
    });
  }

  private submitCashRequest(request: AddCashResult) {
    // Submit the cash request to your backend
    console.log('Submitting cash request:', request);
    // this.requestService.submitRequest(request);
  }
}
```

## Toolbar Integration

```typescript
// Add to any component's toolbar
@Component({
  template: `
    <mat-toolbar>
      <span>Inventory Management</span>
      <span class="spacer"></span>
      <button mat-icon-button (click)="openAddCashModal()" matTooltip="Add Cash">
        <mat-icon>account_balance_wallet</mat-icon>
      </button>
    </mat-toolbar>
  `,
  styles: [`
    .spacer { flex: 1 1 auto; }
  `]
})
export class SomeComponent {
  constructor(private addCashModalService: AddCashModalService) {}

  openAddCashModal() {
    this.addCashModalService.openAddCashModal().subscribe(result => {
      if (result) {
        // Handle result
      }
    });
  }
}
```

## Data Flow Integration

### With State Management (if using NgRx)

```typescript
// actions/cash.actions.ts
export const addCash = createAction(
  '[Cash] Add Cash',
  props<{ transaction: AddCashResult }>()
);

// In your component
openAddCashModal() {
  this.addCashModalService.openAddCashModal()
    .subscribe(result => {
      if (result) {
        this.store.dispatch(addCash({ transaction: result }));
      }
    });
}
```

### With Services

```typescript
// services/cash.service.ts
@Injectable({ providedIn: 'root' })
export class CashService {
  addCashTransaction(transaction: AddCashResult): Observable<any> {
    return this.http.post('/api/cash/transactions', transaction);
  }
}

// In your component
constructor(
  private addCashModalService: AddCashModalService,
  private cashService: CashService
) {}

openAddCashModal() {
  this.addCashModalService.openAddCashModal()
    .subscribe(result => {
      if (result) {
        this.cashService.addCashTransaction(result)
          .subscribe(response => {
            console.log('Transaction saved:', response);
          });
      }
    });
}
```

## Error Handling

```typescript
openAddCashModal() {
  this.addCashModalService.openAddCashModal()
    .subscribe({
      next: (result) => {
        if (result) {
          this.processCashTransaction(result);
        }
      },
      error: (error) => {
        console.error('Error opening modal:', error);
        this.snackBar.open('Error opening cash modal', 'Close', {
          duration: 3000
        });
      }
    });
}
```

## Customization Tips

1. **Custom Denominations**: Modify the `denominations` array in the component
2. **Additional Validation**: Extend the form validators
3. **Custom Styling**: Override the SCSS variables
4. **Additional Fields**: Extend the `AddCashResult` interface and form

## Best Practices

1. Always handle the modal result in a subscription
2. Provide meaningful titles for different contexts
3. Filter cash types based on user permissions
4. Show loading states during API calls
5. Provide user feedback after successful operations
