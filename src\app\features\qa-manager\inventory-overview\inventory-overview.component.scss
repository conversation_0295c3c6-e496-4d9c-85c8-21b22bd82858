.inventory-overview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  h2 {
    margin: 0 0 30px 0;
    color: #1a365d;
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    padding: 2rem 0 1rem 0;
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background: linear-gradient(135deg, #CC0000 0%, #FF6B6B 100%);
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
      padding: 1.5rem 0 0.75rem 0;
    }

    @media (max-width: 480px) {
      font-size: 1.75rem;
      padding: 1rem 0 0.5rem 0;
    }
  }

  .content {
    padding: 1rem 2rem 0.5rem 2rem;
    max-width: 1800px;
    margin: 0 auto;
    width: 100%;
    background: transparent;

    // Large screens - utilize more space
    @media (min-width: 1600px) {
      max-width: 95%;
      padding: 1rem 3rem 0.5rem 3rem;
    }

    @media (min-width: 1200px) and (max-width: 1599px) {
      max-width: 1600px;
      padding: 1rem 2.5rem 0.5rem 2.5rem;
    }

    @media (min-width: 900px) and (max-width: 1199px) {
      max-width: 1200px;
      padding: 1rem 2rem 0.5rem 2rem;
    }

    @media (max-width: 768px) {
      padding: 1rem 1rem 0.5rem 1rem;
    }
  }
}

// Modern Hero Section
.hero-section {
  margin-bottom: 2rem;
}

// Modern Summary Grid
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 4 columns
  @media (min-width: 1600px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Modern Summary Cards
.modern-summary-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0,0,0,0.15);
  }

  .card-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    opacity: 0.08;
    transform: translate(40px, -40px);
  }

  .card-content {
    position: relative;
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      mat-icon {
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        color: white;
      }

      .alert-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 20px;
        height: 20px;
        background-color: #CC0000;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      }
    }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;

      &.positive {
        background: rgba(76, 175, 80, 0.1);
        color: #4CAF50;
      }

      &.neutral {
        background: rgba(158, 158, 158, 0.1);
        color: #9E9E9E;
      }

      &.warning {
        background: rgba(255, 152, 0, 0.1);
        color: #FF9800;
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;
      }
    }
  }

  .card-body {
    .primary-value {
      font-size: 2rem;
      font-weight: 700;
      color: #1a365d;
      line-height: 1;
      margin-bottom: 0.5rem;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }

    .card-label {
      font-size: 1rem;
      font-weight: 600;
      color: #1a365d;
      margin-bottom: 0.25rem;
    }

    .card-description {
      font-size: 0.875rem;
      color: #718096;
      font-weight: 500;
    }
  }

  // Card-specific styling
  &.value-card {
    .card-background {
      background: #4CAF50;
    }
    .card-icon {
      background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
    }
  }

  &.notes-card {
    .card-background {
      background: #2196F3;
    }
    .card-icon {
      background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    }
  }

  &.alerts-card {
    .card-background {
      background: #FFC107;
    }
    .card-icon {
      background: linear-gradient(135deg, #FFC107 0%, #f57c00 100%);
    }

    &.critical {
      .card-background {
        background: #f44336;
      }
      .card-icon {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      }
    }
  }

  &.series-card {
    .card-background {
      background: #1a365d;
    }
    .card-icon {
      background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    }
  }
}

// Modern Inventory Section
.inventory-section {
  .inventory-container {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;

    // Add subtle gradient overlay
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 200px;
      background: linear-gradient(135deg, rgba(26, 54, 93, 0.02) 0%, rgba(33, 150, 243, 0.02) 100%);
      pointer-events: none;
      z-index: 1;
    }
  }

  .section-header {
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    color: white;
    padding: 4rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 200px;

    // Animated gradient background
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;

    // Add floating particles effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.15) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.08) 1.5px, transparent 1.5px);
      background-size: 50px 50px, 30px 30px, 70px 70px;
      animation: floatParticles 15s linear infinite;
      opacity: 0.6;
    }

    // Add glowing orb effect
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      transform: translate(-50%, -50%);
      animation: pulse 4s ease-in-out infinite;
      border-radius: 50%;
    }

    @media (max-width: 768px) {
      padding: 3rem 1.5rem;
      min-height: 160px;
    }

    .section-title {
      font-size: 3rem;
      font-weight: 900;
      margin: 0 0 1rem 0;
      color: white !important;
      letter-spacing: -1px;
      position: relative;
      z-index: 3;
      text-shadow: 0 4px 20px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #ffffff 0%, #f0f8ff 50%, #ffffff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 200% 200%;
      animation: textShimmer 3s ease-in-out infinite;

      @media (max-width: 768px) {
        font-size: 2.2rem;
        margin-bottom: 0.8rem;
      }

      @media (max-width: 480px) {
        font-size: 1.8rem;
      }
    }

    .section-subtitle {
      font-size: 1.3rem;
      margin: 0;
      opacity: 0.95;
      font-weight: 300;
      color: rgba(255, 255, 255, 0.9) !important;
      position: relative;
      z-index: 3;
      text-shadow: 0 2px 10px rgba(0,0,0,0.2);
      max-width: 600px;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1.1rem;
        max-width: 400px;
      }

      @media (max-width: 480px) {
        font-size: 1rem;
      }
    }
  }

  .inventory-content {
    padding: 0;
    position: relative;
    z-index: 2;

    .modern-tabs {
      ::ng-deep .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: none;
          background: linear-gradient(135deg, #f1f5f9 0%, #ffffff 100%);
          padding: 0.8rem 2rem 0;
          box-shadow: 0 12px 48px rgba(102, 126, 234, 0.1);
          position: relative;
          border-radius: 28px 28px 0 0;
          overflow: visible;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(245, 87, 108, 0.03) 100%);
            z-index: 0;
            border-radius: 28px 28px 0 0;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 20%, rgba(245, 87, 108, 0.2) 80%, transparent 100%);
          }

          // Add a subtle glow effect for the header
          box-shadow:
            0 12px 48px rgba(102, 126, 234, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

          .mat-mdc-tab {
            min-width: 240px;
            font-weight: 600;
            color: #64748b;
            text-transform: none;
            font-size: 1.2rem;
            padding: 2.2rem 2.5rem 1.8rem;
            margin: 0 0.3rem;
            border-radius: 24px 24px 0 0;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            z-index: 1;
            backdrop-filter: blur(10px);
            letter-spacing: 0.3px;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(248, 250, 252, 0.8) 100%);
              opacity: 0;
              transition: all 0.5s ease;
              border-radius: 24px 24px 0 0;
              border: 1px solid rgba(255, 255, 255, 0.2);
            }

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 50%;
              width: 0;
              height: 4px;
              background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f5576c 100%);
              transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
              transform: translateX(-50%);
              border-radius: 0 0 4px 4px;
            }

            &:hover {
              transform: translateY(-6px) scale(1.03);
              color: #475569;
              box-shadow: 0 16px 48px rgba(102, 126, 234, 0.18);

              &::before {
                opacity: 1;
                box-shadow: 0 12px 40px rgba(102, 126, 234, 0.12);
              }

              &::after {
                width: 70%;
                height: 5px;
              }
            }

            &.mdc-tab--active {
              color: #1e293b;
              background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
              transform: translateY(-16px) scale(1.1);
              font-weight: 800;
              font-size: 1.3rem;
              border: 3px solid rgba(102, 126, 234, 0.3);
              z-index: 10;
              animation: active-tab-glow 3s ease-in-out infinite;

              &::before {
                opacity: 1;
                background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
                border: 2px solid rgba(102, 126, 234, 0.2);
                backdrop-filter: blur(20px);
              }

              &::after {
                width: 95%;
                height: 8px;
                animation: active-indicator-pulse 2.5s ease-in-out infinite;
                border-radius: 0 0 8px 8px;
              }

              // Add distinctive visual elements for active state
              &:before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(135deg,
                  rgba(102, 126, 234, 0.1) 0%,
                  rgba(245, 87, 108, 0.1) 50%,
                  rgba(102, 126, 234, 0.1) 100%);
                border-radius: 27px 27px 0 0;
                z-index: -1;
                animation: gradient-shift 4s ease-in-out infinite;
                background-size: 200% 200%;
              }

              // Add a subtle inner shadow for depth
              box-shadow:
                0 24px 70px rgba(102, 126, 234, 0.3),
                0 0 50px rgba(102, 126, 234, 0.2),
                inset 0 2px 4px rgba(255, 255, 255, 0.8),
                inset 0 -1px 2px rgba(102, 126, 234, 0.1);

              // Text shadow for better readability
              text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
            }
          }

          .mat-ink-bar {
            display: none; // We're using custom ::after pseudo-element instead
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              padding: 0;
              background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
              border-radius: 0 0 28px 28px;
              overflow: hidden;
              position: relative;

              // Add a subtle glow effect for the content area
              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg,
                  transparent 0%,
                  rgba(102, 126, 234, 0.1) 20%,
                  rgba(245, 87, 108, 0.1) 50%,
                  rgba(102, 126, 234, 0.1) 80%,
                  transparent 100%);
                z-index: 1;
              }

              // Add subtle animation for content appearance
              animation: slide-in-up 0.6s ease-out;
            }

            &.mat-mdc-tab-body-active {
              .mat-mdc-tab-body-content {
                background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
                box-shadow: inset 0 4px 12px rgba(102, 126, 234, 0.05);
              }
            }
          }
        }
      }
    }
}

// Tab Content
.tab-content {
  padding: 4rem 3rem 3rem 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 50%, #edf2f7 100%);
  position: relative;
  min-height: 500px;

  // Add beautiful geometric pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, rgba(245, 87, 108, 0.03) 1.5px, transparent 1.5px),
      linear-gradient(45deg, transparent 49%, rgba(102, 126, 234, 0.01) 50%, transparent 51%);
    background-size: 40px 40px, 60px 60px, 20px 20px;
    pointer-events: none;
    opacity: 0.7;
    animation: subtleFloat 20s ease-in-out infinite;
  }

  // Add a subtle top border with gradient
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    opacity: 0.6;
  }

  @media (max-width: 768px) {
    padding: 2.5rem 1.5rem 2rem 1.5rem;
    min-height: 350px;
  }

  @media (max-width: 480px) {
    padding: 2rem 1rem 1.5rem 1rem;
  }
}

// Series Overview
.series-overview {
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
  padding: 2rem 1rem;

  .series-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0 0.5rem;
    }

    @media (min-width: 1200px) {
      gap: 2.5rem;
    }
  }

  .series-stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 1) 100%);
    border-radius: 24px;
    padding: 2.5rem 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 12px 40px rgba(0,0,0,0.06);
    border: 1px solid rgba(255, 255, 255, 0.8);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    cursor: pointer;

    // Animated gradient background
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.03) 0%,
        rgba(245, 87, 108, 0.03) 25%,
        rgba(76, 175, 80, 0.03) 50%,
        rgba(255, 152, 0, 0.03) 75%,
        rgba(156, 39, 176, 0.03) 100%);
      opacity: 0;
      transition: all 0.5s ease;
      z-index: 0;
    }

    // Floating orb effect
    &::after {
      content: '';
      position: absolute;
      top: -50px;
      right: -50px;
      width: 120px;
      height: 120px;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.15) 0%, transparent 70%);
      border-radius: 50%;
      transform: scale(0.8);
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
    }

    &:hover {
      transform: translateY(-12px) scale(1.03);
      box-shadow: 0 24px 60px rgba(0,0,0,0.12);
      border-color: rgba(102, 126, 234, 0.2);

      &::before {
        opacity: 1;
      }

      &::after {
        transform: scale(1.2) translate(-10px, 10px);
        background: radial-gradient(circle, rgba(102, 126, 234, 0.25) 0%, transparent 70%);
      }

      .stat-icon {
        transform: scale(1.15) rotate(5deg);
        box-shadow: 0 16px 40px rgba(102, 126, 234, 0.4);
      }

      .stat-value {
        transform: scale(1.05);
      }
    }

    .stat-icon {
      width: 72px;
      height: 72px;
      border-radius: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      z-index: 3;

      &::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: linear-gradient(135deg, #667eea, #764ba2, #f5576c);
        border-radius: 22px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      mat-icon {
        color: white;
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        transition: all 0.3s ease;
      }
    }

    .stat-content {
      flex: 1;
      position: relative;
      z-index: 3;

      .stat-value {
        font-size: 2.2rem;
        font-weight: 800;
        color: #1e293b;
        line-height: 1.1;
        margin-bottom: 0.75rem;
        background: linear-gradient(135deg, #1e293b 0%, #667eea 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        transition: all 0.3s ease;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        letter-spacing: 0.5px;
      }

      .stat-label {
        font-size: 1rem;
        color: #64748b;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1.2px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 0;
          height: 2px;
          background: linear-gradient(90deg, #667eea 0%, #f5576c 100%);
          transition: width 0.3s ease;
        }
      }
    }

    // Hover effect for stat label underline
    &:hover .stat-content .stat-label::after {
      width: 100%;
    }
  }
}

// Modern Inventory Grid
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// Modern Inventory Item Cards
.inventory-item {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 12px 40px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,0,0,0.03);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // Add beautiful gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #9C27B0 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // Add subtle background pattern
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, transparent 70%);
    transform: translate(50px, -50px);
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 24px 60px rgba(0,0,0,0.15);

    &::before {
      opacity: 1;
    }

    &::after {
      transform: translate(30px, -30px) scale(1.2);
    }
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;

    .denomination-info {
      display: flex;
      align-items: center;
      gap: 1.5rem;

      .denomination-icon {
        width: 100px;
        height: 60px;
        border-radius: 16px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        border: 3px solid rgba(255,255,255,0.9);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05) rotate(2deg);
          box-shadow: 0 12px 32px rgba(0,0,0,0.15);
        }

        .note-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }

        .fallback-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
          color: #4CAF50;
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        // Add beautiful shimmer effect
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }

        // Add decorative corner accent
        &::after {
          content: '';
          position: absolute;
          top: 8px;
          right: 8px;
          width: 12px;
          height: 12px;
          background: linear-gradient(135deg, #4CAF50 0%, #2196F3 100%);
          border-radius: 50%;
          opacity: 0.7;
        }
      }

      .denomination-details {
        flex: 1;

        .denomination-title {
          font-size: 1.5rem;
          font-weight: 800;
          color: #1a365d;
          margin: 0 0 0.5rem 0;
          background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: -0.5px;
        }

        .denomination-series {
          font-size: 0.95rem;
          color: #718096;
          margin: 0 0 0.75rem 0;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .denomination-value {
          font-size: 0.875rem;
          font-weight: 700;
          color: #4CAF50;
          background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
          padding: 0.5rem 1rem;
          border-radius: 20px;
          display: inline-block;
          border: 2px solid rgba(76, 175, 80, 0.2);
          box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.25);
          }
        }
      }
    }

    .item-status {
      position: relative;
      z-index: 2;

      mat-chip {
        border-radius: 25px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }

        mat-icon {
          font-size: 1.1rem;
          width: 1.1rem;
          height: 1.1rem;
        }
      }
    }
  }

  .item-body {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;

    .item-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 2rem;

      .metric {
        text-align: center;
        padding: 1.5rem 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        border: 2px solid rgba(0,0,0,0.03);
        box-shadow: 0 8px 24px rgba(0,0,0,0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #4CAF50 0%, #2196F3 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 32px rgba(0,0,0,0.12);

          &::before {
            opacity: 1;
          }
        }

        .metric-value {
          font-size: 1.75rem;
          font-weight: 800;
          color: #1a365d;
          line-height: 1;
          margin-bottom: 0.5rem;
          background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .metric-label {
          font-size: 0.95rem;
          color: #718096;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }

    .item-actions {
      text-align: center;

      .add-btn {
        border-radius: 25px;
        font-weight: 700;
        text-transform: none;
        padding: 1rem 2rem;
        font-size: 0.95rem;
        box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
        background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
        border: none;
        color: white;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
          transition: left 0.5s ease;
        }

        &:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 12px 32px rgba(33, 150, 243, 0.4);

          &::before {
            left: 100%;
          }
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .stock-progress {
    position: relative;
    z-index: 2;
    margin-top: 1rem;

    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      ::ng-deep .mat-mdc-progress-bar-buffer {
        background: rgba(0,0,0,0.05);
      }
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.75rem;

      span {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Enhanced Status Chips
mat-chip {
  &.status-in-stock {
    background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
    border: 2px solid rgba(76, 175, 80, 0.2);
  }

  &.status-watch {
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
    border: 2px solid rgba(33, 150, 243, 0.2);
  }

  &.status-medium {
    background: linear-gradient(135deg, #FFC107 0%, #f57c00 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    border: 2px solid rgba(255, 193, 7, 0.2);
  }

  &.status-low {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
    border: 2px solid rgba(255, 152, 0, 0.2);
  }

  &.status-critical {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
    border: 2px solid rgba(244, 67, 54, 0.2);
    animation: pulse 2s ease-in-out infinite;
  }

  &.status-unknown {
    background: linear-gradient(135deg, #718096 0%, #5a6c7d 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4);
    border: 2px solid rgba(113, 128, 150, 0.2);
  }

  &.low-stock {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
    color: #d32f2f !important;
    border: 2px solid rgba(211, 47, 47, 0.2);
    box-shadow: 0 4px 16px rgba(211, 47, 47, 0.2);
  }

  &.normal-stock {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
    color: #388e3c !important;
    border: 2px solid rgba(56, 142, 60, 0.2);
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.2);
  }

  &.high-stock {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    color: #1976d2 !important;
    border: 2px solid rgba(25, 118, 210, 0.2);
    box-shadow: 0 4px 16px rgba(25, 118, 210, 0.2);
  }
}

// Modern No Data State
.no-data {
  text-align: center;
  padding: 5rem 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24px;
  margin: 3rem 0;
  border: 2px solid rgba(0,0,0,0.03);
  box-shadow: 0 12px 40px rgba(0,0,0,0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(33, 150, 243, 0.03) 0%, transparent 70%);
    pointer-events: none;
  }

  .no-data-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem auto;
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12px 32px rgba(33, 150, 243, 0.3);
    position: relative;
    z-index: 2;

    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(25, 118, 210, 0.2) 100%);
      border-radius: 50%;
      z-index: -1;
      animation: pulse 3s ease-in-out infinite;
    }

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: white;
      filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
    }
  }

  h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: #1a365d;
    margin: 0 0 1rem 0;
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 2;
  }

  p {
    font-size: 1.1rem;
    color: #718096;
    margin: 0 0 3rem 0;
    font-weight: 500;
    position: relative;
    z-index: 2;
  }

  button {
    border-radius: 25px;
    font-weight: 700;
    text-transform: none;
    padding: 1rem 2.5rem;
    font-size: 1rem;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    border: none;
    color: white;
    position: relative;
    z-index: 2;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 32px rgba(33, 150, 243, 0.4);

      &::before {
        left: 100%;
      }
    }
  }
}

// Series-Specific Styling with Enhanced Themes
.series-overview {
  // Mandela Series Theme
  &.mandela-theme {
    .series-stat-card {
      &::after {
        background: radial-gradient(circle, rgba(76, 175, 80, 0.15) 0%, transparent 70%);
      }

      &:hover::after {
        background: radial-gradient(circle, rgba(76, 175, 80, 0.25) 0%, transparent 70%);
      }

      .stat-icon {
        background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
        box-shadow: 0 12px 32px rgba(76, 175, 80, 0.3);

        &:hover {
          box-shadow: 0 16px 40px rgba(76, 175, 80, 0.4);
        }
      }

      .stat-value {
        background: linear-gradient(135deg, #1e293b 0%, #4CAF50 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  // Big 5 Series Theme
  &.big5-theme {
    .series-stat-card {
      &::after {
        background: radial-gradient(circle, rgba(255, 152, 0, 0.15) 0%, transparent 70%);
      }

      &:hover::after {
        background: radial-gradient(circle, rgba(255, 152, 0, 0.25) 0%, transparent 70%);
      }

      .stat-icon {
        background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        box-shadow: 0 12px 32px rgba(255, 152, 0, 0.3);

        &:hover {
          box-shadow: 0 16px 40px rgba(255, 152, 0, 0.4);
        }
      }

      .stat-value {
        background: linear-gradient(135deg, #1e293b 0%, #FF9800 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  // Commemorative Series Theme
  &.commemorative-theme {
    .series-stat-card {
      &::after {
        background: radial-gradient(circle, rgba(156, 39, 176, 0.15) 0%, transparent 70%);
      }

      &:hover::after {
        background: radial-gradient(circle, rgba(156, 39, 176, 0.25) 0%, transparent 70%);
      }

      .stat-icon {
        background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
        box-shadow: 0 12px 32px rgba(156, 39, 176, 0.3);

        &:hover {
          box-shadow: 0 16px 40px rgba(156, 39, 176, 0.4);
        }
      }

      .stat-value {
        background: linear-gradient(135deg, #1e293b 0%, #9C27B0 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  // V6 Series Theme
  &.v6-theme {
    .series-stat-card {
      &::after {
        background: radial-gradient(circle, rgba(33, 150, 243, 0.15) 0%, transparent 70%);
      }

      &:hover::after {
        background: radial-gradient(circle, rgba(33, 150, 243, 0.25) 0%, transparent 70%);
      }

      .stat-icon {
        background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        box-shadow: 0 12px 32px rgba(33, 150, 243, 0.3);

        &:hover {
          box-shadow: 0 16px 40px rgba(33, 150, 243, 0.4);
        }
      }

      .stat-value {
        background: linear-gradient(135deg, #1e293b 0%, #2196F3 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

// Enhanced Inventory Item Styling
.inventory-item {
  &.mandela-series {
    border-left: 4px solid #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(255, 255, 255, 1) 100%);

    .denomination-icon {
      border-color: rgba(76, 175, 80, 0.3);
    }
  }

  &.big5-series {
    border-left: 4px solid #FF9800;
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.02) 0%, rgba(255, 255, 255, 1) 100%);

    .denomination-icon {
      border-color: rgba(255, 152, 0, 0.3);
    }
  }

  &.commemorative-series {
    border-left: 4px solid #9C27B0;
    background: linear-gradient(135deg, rgba(156, 39, 176, 0.02) 0%, rgba(255, 255, 255, 1) 100%);

    .denomination-icon {
      border-color: rgba(156, 39, 176, 0.3);
    }
  }

  &.v6-series {
    border-left: 4px solid #2196F3;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.02) 0%, rgba(255, 255, 255, 1) 100%);

    .denomination-icon {
      border-color: rgba(33, 150, 243, 0.3);
    }
  }
}

// Enhanced fallback icon styling
.fallback-icon {
  display: none;
  color: #4CAF50;
  opacity: 0.7;
}

// Enhanced Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 16px 40px rgba(102, 126, 234, 0.5);
  }
}

@keyframes active-tab-glow {
  0%, 100% {
    box-shadow:
      0 20px 60px rgba(102, 126, 234, 0.25),
      0 0 40px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
  50% {
    box-shadow:
      0 24px 70px rgba(102, 126, 234, 0.35),
      0 0 50px rgba(102, 126, 234, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
}

@keyframes active-indicator-pulse {
  0%, 100% {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 30%, #f5576c 70%, #667eea 100%);
    box-shadow:
      0 6px 24px rgba(102, 126, 234, 0.5),
      0 0 20px rgba(102, 126, 234, 0.3);
  }
  50% {
    background: linear-gradient(90deg, #f5576c 0%, #667eea 30%, #764ba2 70%, #f5576c 100%);
    box-shadow:
      0 8px 32px rgba(245, 87, 108, 0.6),
      0 0 30px rgba(245, 87, 108, 0.4);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.95) translateY(5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// Apply animations to elements
.series-stat-card {
  animation: slide-in-up 0.6s ease-out;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  .stat-icon {
    animation: bounce-in 0.8s ease-out;
    animation-delay: 0.3s;
    animation-fill-mode: both;

    &:hover {
      animation: pulse-glow 2s ease-in-out infinite;
    }
  }
}

// Tab content animation
.tab-content {
  animation: slide-in-up 0.5s ease-out;
}

// Shimmer effect for loading states
.shimmer-effect {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

// Enhanced Mobile Responsiveness
@media (max-width: 480px) {
  .modern-tabs {
    ::ng-deep .mat-mdc-tab-group {
      .mat-mdc-tab-header {
        padding: 0.4rem 1rem 0;

        .mat-mdc-tab {
          min-width: 180px;
          font-size: 1.1rem;
          padding: 1.8rem 1.2rem 1.2rem;
          margin: 0 0.15rem;

          &::after {
            height: 4px;
          }

          &.mdc-tab--active {
            font-size: 1.15rem;
            transform: translateY(-10px) scale(1.05);

            &::after {
              height: 6px;
            }
          }
        }
      }
    }
  }

  .series-overview {
    padding: 1rem 0.5rem;

    .series-stats-grid {
      gap: 1rem;
      grid-template-columns: 1fr;
    }

    .series-stat-card {
      padding: 1.5rem 1rem;
      gap: 1rem;
      border-radius: 16px;

      .stat-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }

      .stat-content {
        .stat-value {
          font-size: 1.8rem;
        }

        .stat-label {
          font-size: 0.9rem;
          letter-spacing: 0.8px;
        }
      }

      &:hover {
        transform: translateY(-6px) scale(1.02);
      }
    }
  }
}

// Tablet specific optimizations
@media (min-width: 481px) and (max-width: 768px) {
  .series-overview {
    .series-stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
      max-width: 600px;
    }

    .series-stat-card {
      padding: 2rem 1.5rem;

      .stat-icon {
        width: 64px;
        height: 64px;
      }

      .stat-content .stat-value {
        font-size: 1.75rem;
      }
    }
  }
}

// Large screen optimizations
@media (min-width: 1400px) {
  .series-overview {
    .series-stats-grid {
      max-width: 900px;
      gap: 3rem;
    }

    .series-stat-card {
      padding: 3rem 2.5rem;

      .stat-icon {
        width: 80px;
        height: 80px;
        border-radius: 24px;

        mat-icon {
          font-size: 2.25rem;
          width: 2.25rem;
          height: 2.25rem;
        }
      }

      .stat-content .stat-value {
        font-size: 2.25rem;
      }
    }
  }
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .series-stat-card {
    &:active {
      transform: translateY(-8px) scale(0.98);
      transition: all 0.1s ease;
    }
  }

  .modern-tabs ::ng-deep .mat-mdc-tab {
    &:active {
      transform: translateY(-2px) scale(0.98);
    }
  }
}

// High DPI display optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .series-stat-card {
    .stat-icon mat-icon {
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    }
  }
}

// Dark mode support (if needed in the future)
@media (prefers-color-scheme: dark) {
  .series-stat-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 1) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;

    .stat-value {
      color: #f1f5f9;
    }

    .stat-label {
      color: #94a3b8;
    }
  }
}

// Accessibility improvements
.series-stat-card {
  // Focus styles for keyboard navigation
  &:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    border: 2px solid #000;

    .stat-value {
      color: #000;
      -webkit-text-fill-color: #000;
    }

    .stat-label {
      color: #333;
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    animation: none;

    &:hover {
      transform: none;
    }

    .stat-icon {
      animation: none;
      transition: none;

      &:hover {
        transform: none;
        animation: none;
      }
    }

    &::before,
    &::after {
      transition: none;
      animation: none;
    }
  }
}

// Performance optimizations
.series-stat-card {
  // Use GPU acceleration for smooth animations
  will-change: transform, box-shadow;

  // Optimize repaints
  contain: layout style paint;

  &:hover {
    will-change: transform, box-shadow;
  }
}

// Print styles
@media print {
  .series-stat-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;

    &::before,
    &::after {
      display: none !important;
    }

    .stat-value {
      color: #000 !important;
      -webkit-text-fill-color: #000 !important;
    }

    .stat-label {
      color: #666 !important;
    }
  }

  .modern-tabs ::ng-deep .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      background: white !important;
      box-shadow: none !important;
      border-bottom: 1px solid #ccc !important;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}


