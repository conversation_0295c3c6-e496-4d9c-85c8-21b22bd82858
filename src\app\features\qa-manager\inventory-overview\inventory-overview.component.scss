.inventory-overview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  h2 {
    margin: 0 0 30px 0;
    color: #1a365d;
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    padding: 2rem 0 1rem 0;
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background: linear-gradient(135deg, #CC0000 0%, #FF6B6B 100%);
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
      padding: 1.5rem 0 0.75rem 0;
    }

    @media (max-width: 480px) {
      font-size: 1.75rem;
      padding: 1rem 0 0.5rem 0;
    }
  }

  .content {
    padding: 1rem 2rem 0.5rem 2rem;
    max-width: 1800px;
    margin: 0 auto;
    width: 100%;
    background: transparent;

    // Large screens - utilize more space
    @media (min-width: 1600px) {
      max-width: 95%;
      padding: 1rem 3rem 0.5rem 3rem;
    }

    @media (min-width: 1200px) and (max-width: 1599px) {
      max-width: 1600px;
      padding: 1rem 2.5rem 0.5rem 2.5rem;
    }

    @media (min-width: 900px) and (max-width: 1199px) {
      max-width: 1200px;
      padding: 1rem 2rem 0.5rem 2rem;
    }

    @media (max-width: 768px) {
      padding: 1rem 1rem 0.5rem 1rem;
    }
  }
}

// Modern Hero Section
.hero-section {
  margin-bottom: 2rem;
}

// Modern Summary Grid
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 4 columns
  @media (min-width: 1600px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Modern Summary Cards
.modern-summary-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0,0,0,0.15);
  }

  .card-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    opacity: 0.08;
    transform: translate(40px, -40px);
  }

  .card-content {
    position: relative;
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      mat-icon {
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        color: white;
      }

      .alert-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 20px;
        height: 20px;
        background-color: #CC0000;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      }
    }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;

      &.positive {
        background: rgba(76, 175, 80, 0.1);
        color: #4CAF50;
      }

      &.neutral {
        background: rgba(158, 158, 158, 0.1);
        color: #9E9E9E;
      }

      &.warning {
        background: rgba(255, 152, 0, 0.1);
        color: #FF9800;
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;
      }
    }
  }

  .card-body {
    .primary-value {
      font-size: 2rem;
      font-weight: 700;
      color: #1a365d;
      line-height: 1;
      margin-bottom: 0.5rem;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }

    .card-label {
      font-size: 1rem;
      font-weight: 600;
      color: #1a365d;
      margin-bottom: 0.25rem;
    }

    .card-description {
      font-size: 0.875rem;
      color: #718096;
      font-weight: 500;
    }
  }

  // Card-specific styling
  &.value-card {
    .card-background {
      background: #4CAF50;
    }
    .card-icon {
      background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
    }
  }

  &.notes-card {
    .card-background {
      background: #2196F3;
    }
    .card-icon {
      background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    }
  }

  &.alerts-card {
    .card-background {
      background: #FFC107;
    }
    .card-icon {
      background: linear-gradient(135deg, #FFC107 0%, #f57c00 100%);
    }

    &.critical {
      .card-background {
        background: #f44336;
      }
      .card-icon {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      }
    }
  }

  &.series-card {
    .card-background {
      background: #1a365d;
    }
    .card-icon {
      background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    }
  }
}

// Modern Inventory Section
.inventory-section {
  .inventory-container {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;

    // Add subtle gradient overlay
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 200px;
      background: linear-gradient(135deg, rgba(26, 54, 93, 0.02) 0%, rgba(33, 150, 243, 0.02) 100%);
      pointer-events: none;
      z-index: 1;
    }
  }

  .section-header {
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    color: white;
    padding: 4rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 200px;

    // Animated gradient background
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;

    // Add floating particles effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.15) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.08) 1.5px, transparent 1.5px);
      background-size: 50px 50px, 30px 30px, 70px 70px;
      animation: floatParticles 15s linear infinite;
      opacity: 0.6;
    }

    // Add glowing orb effect
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      transform: translate(-50%, -50%);
      animation: pulse 4s ease-in-out infinite;
      border-radius: 50%;
    }

    @media (max-width: 768px) {
      padding: 3rem 1.5rem;
      min-height: 160px;
    }

    .section-title {
      font-size: 3rem;
      font-weight: 900;
      margin: 0 0 1rem 0;
      color: white !important;
      letter-spacing: -1px;
      position: relative;
      z-index: 3;
      text-shadow: 0 4px 20px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #ffffff 0%, #f0f8ff 50%, #ffffff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 200% 200%;
      animation: textShimmer 3s ease-in-out infinite;

      @media (max-width: 768px) {
        font-size: 2.2rem;
        margin-bottom: 0.8rem;
      }

      @media (max-width: 480px) {
        font-size: 1.8rem;
      }
    }

    .section-subtitle {
      font-size: 1.3rem;
      margin: 0;
      opacity: 0.95;
      font-weight: 300;
      color: rgba(255, 255, 255, 0.9) !important;
      position: relative;
      z-index: 3;
      text-shadow: 0 2px 10px rgba(0,0,0,0.2);
      max-width: 600px;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1.1rem;
        max-width: 400px;
      }

      @media (max-width: 480px) {
        font-size: 1rem;
      }
    }
  }

  .inventory-content {
    padding: 0;
    position: relative;
    z-index: 2;

    .modern-tabs {
      ::ng-deep .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: none;
          background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
          padding: 0 2rem;
          box-shadow: 0 4px 30px rgba(102, 126, 234, 0.1);
          position: relative;
          border-radius: 0;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
          }

          .mat-mdc-tab {
            min-width: 200px;
            font-weight: 600;
            color: #4a5568;
            text-transform: none;
            font-size: 1.1rem;
            padding: 1.8rem 1.5rem;
            margin: 0 0.3rem;
            border-radius: 16px 16px 0 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(245, 87, 108, 0.08) 100%);
              opacity: 0;
              transition: all 0.4s ease;
            }

            &:hover {
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
              transform: translateY(-3px);
              color: #667eea;

              &::before {
                opacity: 1;
              }
            }

            &.mdc-tab--active {
              color: #667eea;
              background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
              box-shadow: 0 -6px 30px rgba(102, 126, 234, 0.2);
              transform: translateY(-6px);
              font-weight: 700;

              &::before {
                opacity: 1;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(245, 87, 108, 0.15) 100%);
              }
            }
          }

          .mat-ink-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f5576c 100%);
            height: 4px;
            border-radius: 2px;
            box-shadow: 0 2px 15px rgba(102, 126, 234, 0.4);
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              padding: 0;
              background: white;
            }
          }
        }
      }
    }
  }
}

// Tab Content
.tab-content {
  padding: 4rem 3rem 3rem 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 50%, #edf2f7 100%);
  position: relative;
  min-height: 500px;

  // Add beautiful geometric pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, rgba(245, 87, 108, 0.03) 1.5px, transparent 1.5px),
      linear-gradient(45deg, transparent 49%, rgba(102, 126, 234, 0.01) 50%, transparent 51%);
    background-size: 40px 40px, 60px 60px, 20px 20px;
    pointer-events: none;
    opacity: 0.7;
    animation: subtleFloat 20s ease-in-out infinite;
  }

  // Add a subtle top border with gradient
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    opacity: 0.6;
  }

  @media (max-width: 768px) {
    padding: 2.5rem 1.5rem 2rem 1.5rem;
    min-height: 350px;
  }

  @media (max-width: 480px) {
    padding: 2rem 1rem 1.5rem 1rem;
  }
}

// Series Overview
.series-overview {
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;

  .series-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    max-width: 700px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .series-stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.25rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.03);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    // Add subtle gradient overlay
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background: radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, transparent 70%);
      transform: translate(30px, -30px);
      transition: all 0.3s ease;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 16px 48px rgba(0,0,0,0.15);

      &::before {
        transform: translate(20px, -20px) scale(1.2);
      }

      .stat-icon {
        transform: scale(1.1);
      }
    }

    .stat-icon {
      width: 64px;
      height: 64px;
      border-radius: 18px;
      background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;

      mat-icon {
        color: white;
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
      }
    }

    .stat-content {
      flex: 1;
      position: relative;
      z-index: 2;

      .stat-value {
        font-size: 1.75rem;
        font-weight: 800;
        color: #1a365d;
        line-height: 1;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .stat-label {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Modern Inventory Grid
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// Modern Inventory Item Cards
.inventory-item {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 12px 40px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,0,0,0.03);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // Add beautiful gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #9C27B0 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // Add subtle background pattern
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, transparent 70%);
    transform: translate(50px, -50px);
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 24px 60px rgba(0,0,0,0.15);

    &::before {
      opacity: 1;
    }

    &::after {
      transform: translate(30px, -30px) scale(1.2);
    }
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;

    .denomination-info {
      display: flex;
      align-items: center;
      gap: 1.5rem;

      .denomination-icon {
        width: 100px;
        height: 60px;
        border-radius: 16px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        border: 3px solid rgba(255,255,255,0.9);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05) rotate(2deg);
          box-shadow: 0 12px 32px rgba(0,0,0,0.15);
        }

        .note-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }

        .fallback-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
          color: #4CAF50;
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        // Add beautiful shimmer effect
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }

        // Add decorative corner accent
        &::after {
          content: '';
          position: absolute;
          top: 8px;
          right: 8px;
          width: 12px;
          height: 12px;
          background: linear-gradient(135deg, #4CAF50 0%, #2196F3 100%);
          border-radius: 50%;
          opacity: 0.7;
        }
      }

      .denomination-details {
        flex: 1;

        .denomination-title {
          font-size: 1.5rem;
          font-weight: 800;
          color: #1a365d;
          margin: 0 0 0.5rem 0;
          background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: -0.5px;
        }

        .denomination-series {
          font-size: 0.95rem;
          color: #718096;
          margin: 0 0 0.75rem 0;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .denomination-value {
          font-size: 0.875rem;
          font-weight: 700;
          color: #4CAF50;
          background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
          padding: 0.5rem 1rem;
          border-radius: 20px;
          display: inline-block;
          border: 2px solid rgba(76, 175, 80, 0.2);
          box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.25);
          }
        }
      }
    }

    .item-status {
      position: relative;
      z-index: 2;

      mat-chip {
        border-radius: 25px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }

        mat-icon {
          font-size: 1.1rem;
          width: 1.1rem;
          height: 1.1rem;
        }
      }
    }
  }

  .item-body {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;

    .item-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 2rem;

      .metric {
        text-align: center;
        padding: 1.5rem 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        border: 2px solid rgba(0,0,0,0.03);
        box-shadow: 0 8px 24px rgba(0,0,0,0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #4CAF50 0%, #2196F3 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 32px rgba(0,0,0,0.12);

          &::before {
            opacity: 1;
          }
        }

        .metric-value {
          font-size: 1.75rem;
          font-weight: 800;
          color: #1a365d;
          line-height: 1;
          margin-bottom: 0.5rem;
          background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .metric-label {
          font-size: 0.95rem;
          color: #718096;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }

    .item-actions {
      text-align: center;

      .add-btn {
        border-radius: 25px;
        font-weight: 700;
        text-transform: none;
        padding: 1rem 2rem;
        font-size: 0.95rem;
        box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
        background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
        border: none;
        color: white;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
          transition: left 0.5s ease;
        }

        &:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 12px 32px rgba(33, 150, 243, 0.4);

          &::before {
            left: 100%;
          }
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .stock-progress {
    position: relative;
    z-index: 2;
    margin-top: 1rem;

    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      ::ng-deep .mat-mdc-progress-bar-buffer {
        background: rgba(0,0,0,0.05);
      }
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.75rem;

      span {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Enhanced Status Chips
mat-chip {
  &.status-in-stock {
    background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
    border: 2px solid rgba(76, 175, 80, 0.2);
  }

  &.status-watch {
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
    border: 2px solid rgba(33, 150, 243, 0.2);
  }

  &.status-medium {
    background: linear-gradient(135deg, #FFC107 0%, #f57c00 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    border: 2px solid rgba(255, 193, 7, 0.2);
  }

  &.status-low {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
    border: 2px solid rgba(255, 152, 0, 0.2);
  }

  &.status-critical {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
    border: 2px solid rgba(244, 67, 54, 0.2);
    animation: pulse 2s ease-in-out infinite;
  }

  &.status-unknown {
    background: linear-gradient(135deg, #718096 0%, #5a6c7d 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4);
    border: 2px solid rgba(113, 128, 150, 0.2);
  }

  &.low-stock {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
    color: #d32f2f !important;
    border: 2px solid rgba(211, 47, 47, 0.2);
    box-shadow: 0 4px 16px rgba(211, 47, 47, 0.2);
  }

  &.normal-stock {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
    color: #388e3c !important;
    border: 2px solid rgba(56, 142, 60, 0.2);
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.2);
  }

  &.high-stock {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    color: #1976d2 !important;
    border: 2px solid rgba(25, 118, 210, 0.2);
    box-shadow: 0 4px 16px rgba(25, 118, 210, 0.2);
  }
}

// Modern No Data State
.no-data {
  text-align: center;
  padding: 5rem 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24px;
  margin: 3rem 0;
  border: 2px solid rgba(0,0,0,0.03);
  box-shadow: 0 12px 40px rgba(0,0,0,0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(33, 150, 243, 0.03) 0%, transparent 70%);
    pointer-events: none;
  }

  .no-data-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem auto;
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12px 32px rgba(33, 150, 243, 0.3);
    position: relative;
    z-index: 2;

    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(25, 118, 210, 0.2) 100%);
      border-radius: 50%;
      z-index: -1;
      animation: pulse 3s ease-in-out infinite;
    }

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: white;
      filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
    }
  }

  h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: #1a365d;
    margin: 0 0 1rem 0;
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 2;
  }

  p {
    font-size: 1.1rem;
    color: #718096;
    margin: 0 0 3rem 0;
    font-weight: 500;
    position: relative;
    z-index: 2;
  }

  button {
    border-radius: 25px;
    font-weight: 700;
    text-transform: none;
    padding: 1rem 2.5rem;
    font-size: 1rem;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    border: none;
    color: white;
    position: relative;
    z-index: 2;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 32px rgba(33, 150, 243, 0.4);

      &::before {
        left: 100%;
      }
    }
  }
}

// Series-Specific Styling
.inventory-item {
  &.mandela-series {
    border-left: 4px solid #4CAF50;

    .denomination-icon {
      border-color: rgba(76, 175, 80, 0.3);
    }
  }

  &.big5-series {
    border-left: 4px solid #FF9800;

    .denomination-icon {
      border-color: rgba(255, 152, 0, 0.3);
    }
  }

  &.commemorative-series {
    border-left: 4px solid #9C27B0;

    .denomination-icon {
      border-color: rgba(156, 39, 176, 0.3);
    }
  }

  &.v6-series {
    border-left: 4px solid #2196F3;

    .denomination-icon {
      border-color: rgba(33, 150, 243, 0.3);
    }
  }
}

// Enhanced fallback icon styling
.fallback-icon {
  display: none;
  color: #4CAF50;
  opacity: 0.7;
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}


