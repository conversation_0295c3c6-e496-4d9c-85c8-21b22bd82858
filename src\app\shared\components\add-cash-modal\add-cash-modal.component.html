<div class="add-cash-modal">
  <div class="modal-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon">account_balance_wallet</mat-icon>
      {{ data.title || 'Add Cash' }}
    </h2>
    <button mat-icon-button mat-dialog-close class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div mat-dialog-content class="modal-content">
    <form [formGroup]="addCashForm" (ngSubmit)="onSubmit()" class="cash-form">
      
      <!-- Cash Type Selection -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Cash Type</mat-label>
        <mat-select formControlName="cashTypeId" required>
          <mat-option *ngFor="let cashType of data.cashTypes" [value]="cashType.id">
            {{ cashType.id }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="getFieldError('cashTypeId')">
          {{ getFieldError('cashTypeId') }}
        </mat-error>
      </mat-form-field>

      <!-- Transaction Type -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Transaction Type</mat-label>
        <mat-select formControlName="transactionType" required>
          <mat-option *ngFor="let type of transactionTypes" [value]="type">
            <div class="transaction-option">
              <mat-icon [class]="type === 'Addition' ? 'add-icon' : 'subtract-icon'">
                {{ type === 'Addition' ? 'add_circle' : 'remove_circle' }}
              </mat-icon>
              {{ type }}
            </div>
          </mat-option>
        </mat-select>
        <mat-error *ngIf="getFieldError('transactionType')">
          {{ getFieldError('transactionType') }}
        </mat-error>
      </mat-form-field>

      <!-- Denomination and Quantity Row -->
      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Denomination</mat-label>
          <mat-select formControlName="denomination" required>
            <mat-option *ngFor="let denom of denominations" [value]="denom.value">
              {{ denom.label }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="getFieldError('denomination')">
            {{ getFieldError('denomination') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Quantity</mat-label>
          <input matInput type="number" formControlName="quantity" min="1" required>
          <mat-error *ngIf="getFieldError('quantity')">
            {{ getFieldError('quantity') }}
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Total Value Display -->
      <div class="total-value-card" *ngIf="getTotalValue() > 0">
        <div class="total-label">Total Value:</div>
        <div class="total-amount">R{{ getTotalValue() | number:'1.2-2' }}</div>
      </div>

      <!-- Notes -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Notes (Optional)</mat-label>
        <textarea matInput formControlName="notes" rows="3" placeholder="Add any additional notes..."></textarea>
      </mat-form-field>

    </form>
  </div>

  <div mat-dialog-actions class="modal-actions">
    <button mat-button type="button" (click)="onCancel()" class="cancel-button">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button color="primary" (click)="onSubmit()" 
            [disabled]="!addCashForm.valid" class="submit-button">
      <mat-icon>save</mat-icon>
      {{ addCashForm.get('transactionType')?.value === 'Addition' ? 'Add Cash' : 'Remove Cash' }}
    </button>
  </div>
</div>
