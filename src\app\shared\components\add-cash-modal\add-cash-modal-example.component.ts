import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AddCashModalService } from './add-cash-modal.service';
import type { AddCashResult } from './add-cash-modal.service';
import { CashType } from '../../models/cashtype.model';
import { TransactionType } from '../../models/enums';

@Component({
  selector: 'app-add-cash-modal-example',
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="example-container">
      <h3>Add Cash Modal Examples</h3>
      
      <div class="button-group">
        <button mat-raised-button color="primary" (click)="openBasicModal()">
          <mat-icon>add</mat-icon>
          Open Basic Modal
        </button>
        
        <button mat-raised-button color="accent" (click)="openModalWithTypes()">
          <mat-icon>account_balance_wallet</mat-icon>
          Open with Cash Types
        </button>
        
        <button mat-raised-button color="warn" (click)="openModalForSpecificType()">
          <mat-icon>monetization_on</mat-icon>
          Open for Specific Type
        </button>
      </div>
    </div>
  `,
  styles: [`
    .example-container {
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .button-group {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 20px;
    }
    
    button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class AddCashModalExampleComponent {

  constructor(
    private addCashModalService: AddCashModalService,
    private snackBar: MatSnackBar
  ) {}

  openBasicModal(): void {
    this.addCashModalService.openAddCashModal()
      .subscribe(result => {
        if (result) {
          this.handleResult(result);
        }
      });
  }

  openModalWithTypes(): void {
    const mockCashTypes: CashType[] = [
      { id: 'ZAR_NOTES' },
      { id: 'USD_NOTES' },
      { id: 'EUR_NOTES' }
    ];

    this.addCashModalService.openAddCashModalWithTypes(mockCashTypes, 'Add Cash to Inventory')
      .subscribe(result => {
        if (result) {
          this.handleResult(result);
        }
      });
  }

  openModalForSpecificType(): void {
    const specificCashType: CashType = { id: 'ZAR_NOTES' };

    this.addCashModalService.openAddCashModalForType(specificCashType, 'Add ZAR Notes')
      .subscribe(result => {
        if (result) {
          this.handleResult(result);
        }
      });
  }

  private handleResult(result: AddCashResult): void {
    const action = result.transactionType === TransactionType.Addition ? 'Added' : 'Removed';
    const totalValue = result.denomination * result.quantity;
    
    const message = `${action} ${result.quantity} x R${result.denomination} notes (Total: R${totalValue})`;
    
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });

    console.log('Add Cash Result:', result);
  }
}
