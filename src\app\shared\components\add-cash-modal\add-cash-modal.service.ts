import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { AddCashModalComponent } from './add-cash-modal.component';
import type { AddCashDialogData, AddCashResult } from './add-cash-modal.component';
import { CashType } from '../../models/cashtype.model';

// Re-export interfaces for easier importing
export type { AddCashDialogData, AddCashResult } from './add-cash-modal.component';

@Injectable({
  providedIn: 'root'
})
export class AddCashModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Add Cash modal
   * @param data Configuration data for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCashModal(data?: AddCashDialogData): Observable<AddCashResult | undefined> {
    const dialogRef: MatDialogRef<AddCashModalComponent> = this.dialog.open(
      AddCashModalComponent,
      {
        width: '600px',
        maxWidth: '95vw',
        maxHeight: '90vh',
        disableClose: false,
        autoFocus: true,
        restoreFocus: true,
        data: data || {},
        panelClass: 'add-cash-modal-panel'
      }
    );

    return dialogRef.afterClosed();
  }

  /**
   * Opens the Add Cash modal with specific cash types
   * @param cashTypes Array of available cash types
   * @param title Optional custom title for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCashModalWithTypes(
    cashTypes: CashType[], 
    title?: string
  ): Observable<AddCashResult | undefined> {
    return this.openAddCashModal({
      title: title || 'Add Cash',
      cashTypes: cashTypes
    });
  }

  /**
   * Opens the Add Cash modal for a specific cash type
   * @param cashType The specific cash type to add
   * @param title Optional custom title for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCashModalForType(
    cashType: CashType, 
    title?: string
  ): Observable<AddCashResult | undefined> {
    return this.openAddCashModal({
      title: title || `Add ${cashType.id}`,
      cashTypes: [cashType]
    });
  }

  /**
   * Checks if any Add Cash modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof AddCashModalComponent
    );
  }

  /**
   * Closes all open Add Cash modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof AddCashModalComponent)
      .forEach(dialog => dialog.close());
  }
}
