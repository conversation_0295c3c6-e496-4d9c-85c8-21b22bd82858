# Add Cash Modal Component

A reusable Angular modal component for adding or removing cash from inventory in the FFA CMS application.

## Features

- **Reactive Forms**: Built with Angular Reactive Forms for robust validation
- **Material Design**: Uses Angular Material components for consistent UI
- **Flexible Configuration**: Supports different cash types and transaction types
- **Real-time Calculations**: Shows total value as user inputs denomination and quantity
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: Follows Angular Material accessibility guidelines

## Usage

### Basic Usage

```typescript
import { AddCashModalService } from '@shared/components';

constructor(private addCashModalService: AddCashModalService) {}

openModal() {
  this.addCashModalService.openAddCashModal()
    .subscribe(result => {
      if (result) {
        console.log('Cash transaction:', result);
        // Handle the result
      }
    });
}
```

### With Specific Cash Types

```typescript
const cashTypes: CashType[] = [
  { id: 'ZAR_NOTES', name: 'South African Rand Notes', currency: 'ZAR' },
  { id: 'USD_NOTES', name: 'US Dollar Notes', currency: 'USD' }
];

this.addCashModalService.openAddCashModalWithTypes(cashTypes, 'Add Cash to Vault')
  .subscribe(result => {
    if (result) {
      // Handle result
    }
  });
```

### For a Specific Cash Type

```typescript
const cashType: CashType = { id: 'ZAR_NOTES', name: 'ZAR Notes' };

this.addCashModalService.openAddCashModalForType(cashType, 'Add ZAR Notes')
  .subscribe(result => {
    if (result) {
      // Handle result
    }
  });
```

## Component Structure

```
add-cash-modal/
├── add-cash-modal.component.ts       # Main component
├── add-cash-modal.component.html     # Template
├── add-cash-modal.component.scss     # Styles
├── add-cash-modal.service.ts         # Service for opening modals
├── add-cash-modal-example.component.ts # Usage examples
├── index.ts                          # Exports
└── README.md                         # This file
```

## Data Interfaces

### AddCashDialogData
```typescript
interface AddCashDialogData {
  title?: string;           // Custom modal title
  cashTypes?: CashType[];   // Available cash types
}
```

### AddCashResult
```typescript
interface AddCashResult {
  cashTypeId: string;           // Selected cash type ID
  denomination: number;         // Note denomination (10, 20, 50, 100, 200)
  quantity: number;             // Number of notes
  transactionType: TransactionType; // Addition or Subtraction
  notes?: string;               // Optional notes
}
```

## Styling

The component uses a modern, card-based design with:
- Gradient backgrounds for primary actions
- Rounded corners and subtle shadows
- Responsive layout that adapts to mobile screens
- Material Design color scheme
- Smooth hover and focus transitions

## Dependencies

- Angular Material Dialog
- Angular Material Form Fields
- Angular Material Input
- Angular Material Select
- Angular Material Button
- Angular Material Icon
- Angular Reactive Forms

## Integration Notes

1. **Import Required Modules**: Ensure Angular Material modules are imported in your app
2. **Provide MatDialog**: The service requires MatDialog to be provided
3. **Styling**: The component includes comprehensive SCSS styling
4. **Validation**: All form fields include proper validation and error messages
5. **Accessibility**: Component follows ARIA guidelines and keyboard navigation

## Example Integration in Feature Components

```typescript
// In your feature component
import { AddCashModalService, AddCashResult } from '@shared/components';

@Component({
  // ... component config
})
export class InventoryComponent {
  constructor(private addCashModalService: AddCashModalService) {}

  addCash() {
    this.addCashModalService.openAddCashModal()
      .subscribe(result => {
        if (result) {
          this.processCashTransaction(result);
        }
      });
  }

  private processCashTransaction(result: AddCashResult) {
    // Implement your business logic here
    // e.g., call API to update inventory
  }
}
```

## Customization

The modal can be customized by:
- Passing different `AddCashDialogData` configurations
- Modifying the SCSS variables for colors and spacing
- Extending the component for additional fields
- Customizing the denomination options in the component
