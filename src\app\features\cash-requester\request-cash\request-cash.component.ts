import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RequestCashService } from './request-cash.service';

@Component({
  selector: 'app-request-cash',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './request-cash.component.html',
  styleUrls: ['./request-cash.component.scss']
})
export class RequestCashComponent implements OnInit {

  constructor(private requestCashService: RequestCashService) { }

  ngOnInit(): void {
    // Initialize component
  }
}
