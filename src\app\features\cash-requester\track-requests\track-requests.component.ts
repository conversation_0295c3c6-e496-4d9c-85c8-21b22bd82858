import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TrackRequestsService } from './track-requests.service';

@Component({
  selector: 'app-track-requests',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './track-requests.component.html',
  styleUrls: ['./track-requests.component.scss']
})
export class TrackRequestsComponent implements OnInit {

  constructor(private trackRequestsService: TrackRequestsService) { }

  ngOnInit(): void {
    // Initialize component
  }
}
