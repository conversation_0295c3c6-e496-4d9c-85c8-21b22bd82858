import { TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { of } from 'rxjs';
import { AddCashModalService } from './add-cash-modal.service';
import { AddCashModalComponent } from './add-cash-modal.component';
import type { AddCashDialogData, AddCashResult } from './add-cash-modal.component';
import { CashType } from '../../models/cashtype.model';
import { TransactionType } from '../../models/enums';

describe('AddCashModalService', () => {
  let service: AddCashModalService;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddCashModalComponent>>;

  const mockCashTypes: CashType[] = [
    { id: 'ZAR_NOTES', name: 'ZAR Notes', currency: 'ZAR' },
    { id: 'USD_NOTES', name: 'USD Notes', currency: 'USD' }
  ];

  const mockResult: AddCashResult = {
    cashTypeId: 'ZAR_NOTES',
    denomination: 100,
    quantity: 5,
    transactionType: TransactionType.Addition,
    notes: 'Test notes'
  };

  beforeEach(() => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed', 'close']);
    mockDialogRef.afterClosed.and.returnValue(of(mockResult));

    mockDialog = jasmine.createSpyObj('MatDialog', ['open', 'closeAll']);
    mockDialog.open.and.returnValue(mockDialogRef);
    mockDialog.openDialogs = [];

    TestBed.configureTestingModule({
      providers: [
        AddCashModalService,
        { provide: MatDialog, useValue: mockDialog }
      ]
    });
    service = TestBed.inject(AddCashModalService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('openAddCashModal', () => {
    it('should open dialog with default configuration', () => {
      service.openAddCashModal();

      expect(mockDialog.open).toHaveBeenCalledWith(
        AddCashModalComponent,
        {
          width: '600px',
          maxWidth: '95vw',
          maxHeight: '90vh',
          disableClose: false,
          autoFocus: true,
          restoreFocus: true,
          data: {},
          panelClass: 'add-cash-modal-panel'
        }
      );
    });

    it('should open dialog with provided data', () => {
      const dialogData: AddCashDialogData = {
        title: 'Custom Title',
        cashTypes: mockCashTypes
      };

      service.openAddCashModal(dialogData);

      expect(mockDialog.open).toHaveBeenCalledWith(
        AddCashModalComponent,
        jasmine.objectContaining({
          data: dialogData
        })
      );
    });

    it('should return observable from dialog afterClosed', () => {
      const result$ = service.openAddCashModal();

      result$.subscribe(result => {
        expect(result).toEqual(mockResult);
      });
    });
  });

  describe('openAddCashModalWithTypes', () => {
    it('should open dialog with cash types and default title', () => {
      service.openAddCashModalWithTypes(mockCashTypes);

      expect(mockDialog.open).toHaveBeenCalledWith(
        AddCashModalComponent,
        jasmine.objectContaining({
          data: {
            title: 'Add Cash',
            cashTypes: mockCashTypes
          }
        })
      );
    });

    it('should open dialog with cash types and custom title', () => {
      const customTitle = 'Custom Add Cash Title';
      service.openAddCashModalWithTypes(mockCashTypes, customTitle);

      expect(mockDialog.open).toHaveBeenCalledWith(
        AddCashModalComponent,
        jasmine.objectContaining({
          data: {
            title: customTitle,
            cashTypes: mockCashTypes
          }
        })
      );
    });
  });

  describe('openAddCashModalForType', () => {
    it('should open dialog for specific cash type with default title', () => {
      const cashType = mockCashTypes[0];
      service.openAddCashModalForType(cashType);

      expect(mockDialog.open).toHaveBeenCalledWith(
        AddCashModalComponent,
        jasmine.objectContaining({
          data: {
            title: `Add ${cashType.id}`,
            cashTypes: [cashType]
          }
        })
      );
    });

    it('should open dialog for specific cash type with custom title', () => {
      const cashType = mockCashTypes[0];
      const customTitle = 'Custom Title';
      service.openAddCashModalForType(cashType, customTitle);

      expect(mockDialog.open).toHaveBeenCalledWith(
        AddCashModalComponent,
        jasmine.objectContaining({
          data: {
            title: customTitle,
            cashTypes: [cashType]
          }
        })
      );
    });
  });

  describe('isModalOpen', () => {
    it('should return false when no dialogs are open', () => {
      mockDialog.openDialogs = [];
      expect(service.isModalOpen()).toBeFalse();
    });

    it('should return true when AddCashModalComponent is open', () => {
      const mockOpenDialog = {
        componentInstance: new AddCashModalComponent(
          jasmine.createSpy(),
          jasmine.createSpy(),
          {}
        )
      };
      mockDialog.openDialogs = [mockOpenDialog as any];
      
      expect(service.isModalOpen()).toBeTrue();
    });

    it('should return false when other dialogs are open but not AddCashModalComponent', () => {
      const mockOpenDialog = {
        componentInstance: {}
      };
      mockDialog.openDialogs = [mockOpenDialog as any];
      
      expect(service.isModalOpen()).toBeFalse();
    });
  });

  describe('closeAllModals', () => {
    it('should close all AddCashModalComponent dialogs', () => {
      const mockAddCashDialog = {
        componentInstance: new AddCashModalComponent(
          jasmine.createSpy(),
          jasmine.createSpy(),
          {}
        ),
        close: jasmine.createSpy()
      };
      
      const mockOtherDialog = {
        componentInstance: {},
        close: jasmine.createSpy()
      };

      mockDialog.openDialogs = [mockAddCashDialog as any, mockOtherDialog as any];
      
      service.closeAllModals();
      
      expect(mockAddCashDialog.close).toHaveBeenCalled();
      expect(mockOtherDialog.close).not.toHaveBeenCalled();
    });
  });
});
