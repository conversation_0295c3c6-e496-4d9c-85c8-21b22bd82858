import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { AddCashModalComponent } from './add-cash-modal.component';
import type { AddCashDialogData } from './add-cash-modal.component';
import { TransactionType } from '../../models/enums';
import { CashType } from '../../models/cashtype.model';

describe('AddCashModalComponent', () => {
  let component: AddCashModalComponent;
  let fixture: ComponentFixture<AddCashModalComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddCashModalComponent>>;

  const mockCashTypes: CashType[] = [
    { id: 'ZAR_NOTES', name: 'ZAR Notes', currency: 'ZAR' },
    { id: 'USD_NOTES', name: 'USD Notes', currency: 'USD' }
  ];

  const mockDialogData: AddCashDialogData = {
    title: 'Test Add Cash',
    cashTypes: mockCashTypes
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        AddCashModalComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddCashModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.addCashForm).toBeDefined();
    expect(component.addCashForm.get('transactionType')?.value).toBe(TransactionType.Addition);
    expect(component.addCashForm.get('cashTypeId')?.value).toBe('');
    expect(component.addCashForm.get('denomination')?.value).toBe('');
    expect(component.addCashForm.get('quantity')?.value).toBe('');
    expect(component.addCashForm.get('notes')?.value).toBe('');
  });

  it('should set cash type if only one is provided', () => {
    const singleCashTypeData: AddCashDialogData = {
      cashTypes: [mockCashTypes[0]]
    };
    
    component.data = singleCashTypeData;
    component.ngOnInit();
    
    expect(component.addCashForm.get('cashTypeId')?.value).toBe('ZAR_NOTES');
  });

  it('should calculate total value correctly', () => {
    component.addCashForm.patchValue({
      denomination: 100,
      quantity: 5
    });

    expect(component.getTotalValue()).toBe(500);
  });

  it('should return 0 for total value when fields are empty', () => {
    expect(component.getTotalValue()).toBe(0);
  });

  it('should validate required fields', () => {
    component.addCashForm.markAllAsTouched();
    
    expect(component.addCashForm.get('cashTypeId')?.errors?.['required']).toBeTruthy();
    expect(component.addCashForm.get('denomination')?.errors?.['required']).toBeTruthy();
    expect(component.addCashForm.get('quantity')?.errors?.['required']).toBeTruthy();
    expect(component.addCashForm.get('transactionType')?.errors).toBeNull();
  });

  it('should validate minimum values for denomination and quantity', () => {
    component.addCashForm.patchValue({
      denomination: 0,
      quantity: 0
    });

    expect(component.addCashForm.get('denomination')?.errors?.['min']).toBeTruthy();
    expect(component.addCashForm.get('quantity')?.errors?.['min']).toBeTruthy();
  });

  it('should close dialog with result when form is valid', () => {
    component.addCashForm.patchValue({
      cashTypeId: 'ZAR_NOTES',
      denomination: 100,
      quantity: 5,
      transactionType: TransactionType.Addition,
      notes: 'Test notes'
    });

    component.onSubmit();

    expect(mockDialogRef.close).toHaveBeenCalledWith({
      cashTypeId: 'ZAR_NOTES',
      denomination: 100,
      quantity: 5,
      transactionType: TransactionType.Addition,
      notes: 'Test notes'
    });
  });

  it('should not close dialog when form is invalid', () => {
    component.onSubmit();
    expect(mockDialogRef.close).not.toHaveBeenCalled();
  });

  it('should close dialog without result when cancelled', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  it('should return correct field error messages', () => {
    component.addCashForm.get('cashTypeId')?.markAsTouched();
    component.addCashForm.get('cashTypeId')?.setErrors({ required: true });

    expect(component.getFieldError('cashTypeId')).toBe('Cash Type is required');
  });

  it('should return empty string when field has no errors', () => {
    expect(component.getFieldError('cashTypeId')).toBe('');
  });

  it('should have correct denominations available', () => {
    expect(component.denominations).toEqual([
      { value: 10, label: 'R10' },
      { value: 20, label: 'R20' },
      { value: 50, label: 'R50' },
      { value: 100, label: 'R100' },
      { value: 200, label: 'R200' }
    ]);
  });

  it('should have correct transaction types available', () => {
    expect(component.transactionTypes).toEqual([
      TransactionType.Addition,
      TransactionType.Subtraction
    ]);
  });
});
