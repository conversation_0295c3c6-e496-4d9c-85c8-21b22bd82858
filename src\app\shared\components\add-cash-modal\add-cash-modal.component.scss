.add-cash-modal {
  min-width: 500px;
  max-width: 600px;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 16px 0;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 24px;

    h2 {
      display: flex;
      align-items: center;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: #333;

      .header-icon {
        margin-right: 12px;
        color: #1976d2;
        font-size: 1.8rem;
        width: 1.8rem;
        height: 1.8rem;
      }
    }

    .close-button {
      color: #666;
      
      &:hover {
        color: #333;
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  .modal-content {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;

    .cash-form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .full-width {
        width: 100%;
      }

      .form-row {
        display: flex;
        gap: 16px;
        align-items: flex-start;

        .half-width {
          flex: 1;
        }
      }

      .transaction-option {
        display: flex;
        align-items: center;
        gap: 8px;

        .add-icon {
          color: #4caf50;
        }

        .subtract-icon {
          color: #f44336;
        }
      }

      .total-value-card {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
        margin: 8px 0;

        .total-label {
          font-size: 1rem;
          font-weight: 500;
          opacity: 0.9;
        }

        .total-amount {
          font-size: 1.5rem;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px 0 0 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 24px;

    .cancel-button {
      color: #666;
      
      mat-icon {
        margin-right: 8px;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .submit-button {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      color: white;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
      
      mat-icon {
        margin-right: 8px;
      }

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
      }

      &:disabled {
        background: #ccc;
        color: #999;
        box-shadow: none;
      }
    }
  }
}

// Form field customizations
::ng-deep {
  .add-cash-modal {
    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        border-radius: 8px;
      }

      &.mat-form-field-appearance-outline {
        .mat-mdc-form-field-outline {
          border-radius: 8px;
        }
      }
    }

    .mat-mdc-select-panel {
      border-radius: 8px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .mat-mdc-option {
      &:hover {
        background-color: rgba(25, 118, 210, 0.08);
      }

      &.mat-mdc-option-active {
        background-color: rgba(25, 118, 210, 0.12);
      }
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .add-cash-modal {
    min-width: 90vw;
    max-width: 95vw;

    .form-row {
      flex-direction: column;
      gap: 12px;

      .half-width {
        width: 100%;
      }
    }

    .modal-actions {
      flex-direction: column-reverse;
      gap: 8px;

      button {
        width: 100%;
      }
    }
  }
}
