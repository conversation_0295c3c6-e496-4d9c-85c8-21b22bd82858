import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InventoryExportService } from './inventory-export.service';

@Component({
  selector: 'app-inventory-export',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './inventory-export.component.html',
  styleUrls: ['./inventory-export.component.scss']
})
export class InventoryExportComponent implements OnInit {

  constructor(private inventoryExportService: InventoryExportService) { }

  ngOnInit(): void {
    // Initialize component
  }
}
