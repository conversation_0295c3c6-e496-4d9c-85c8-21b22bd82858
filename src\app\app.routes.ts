import { Routes } from '@angular/router';
// import { authGuard } from './core/guards/auth.guard';
// import { roleGuard } from './core/guards/role.guard';

export const routes: Routes = [
  {
    "path": "",
    "redirectTo": "/home",
    "pathMatch": "full"
  },
  {
    "path": "cash-requester",
    loadChildren: () => import('./features/cash-requester/cash-requester.routes').then(m => m.cash_requesterRoutes),
    // canActivate: [
    //   authGuard,
    //   roleGuard
    // ],
    // data: {
    //   role: "cash-requester"
    // }
  },
  {
    "path": "cash-issuer",
    loadChildren: () => import('./features/cash-issuer/cash-issuer.routes').then(m => m.cash_issuerRoutes),
    // "canActivate": [
    //   "authGuard",
    //   "roleGuard"
    // ],
    // "data": {
    //   "role": "cash-issuer"
    // }
  },
  {
    "path": "qa-manager",
    loadChildren: () => import('./features/qa-manager/qa-manager.routes').then(m => m.qa_managerRoutes)
    // No auth guards needed for qa-manager
  },
  {
    "path": "**",
    "redirectTo": "/not-found"
  }
];
